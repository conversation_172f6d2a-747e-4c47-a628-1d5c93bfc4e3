import mongoose from "mongoose";
import Item from "../models/item.model";
import { ItemHistory } from "../models/itemHistory.model";
import Logger from "../../utils/logUtils";


export interface StockUpdate {
  itemId: mongoose.Types.ObjectId;
  quantityChange: number;
  operationType: string;
  description?: string;
  orderId?: mongoose.Types.ObjectId;
  batchId?: mongoose.Types.ObjectId;
}

export class AtomicStockManager {
  /**
   * Update stock levels atomically using MongoDB transactions (if supported)
   */
  static async updateStockLevels(
    updates: StockUpdate[],
    restaurantId: mongoose.Types.ObjectId,
    operationId: string
  ): Promise<void> {
    // Check if transactions are supported
    const supportsTransactions = await this.supportsTransactions();

    if (supportsTransactions) {
      return this.updateStockLevelsWithTransaction(updates, restaurantId, operationId);
    } else {
      return this.updateStockLevelsWithoutTransaction(updates, restaurantId, operationId);
    }
  }

  /**
   * Update stock levels with transaction support
   */
  private static async updateStockLevelsWithTransaction(
    updates: StockUpdate[],
    restaurantId: mongoose.Types.ObjectId,
    operationId: string
  ): Promise<void> {
    const session = await mongoose.startSession();

    try {
      await session.withTransaction(async () => {
        await this.performStockUpdates(updates, restaurantId, operationId, session);
      });
    } catch (error) {
      Logger.error('Failed to update stock levels with transaction', {
        error: error instanceof Error ? error.message : 'Unknown error',
        operationId,
        restaurantId: restaurantId.toString(),
        updates
      });
      throw error;
    } finally {
      await session.endSession();
    }
  }

  /**
   * Update stock levels without transaction support (fallback)
   */
  private static async updateStockLevelsWithoutTransaction(
    updates: StockUpdate[],
    restaurantId: mongoose.Types.ObjectId,
    operationId: string
  ): Promise<void> {
    try {
      await this.performStockUpdates(updates, restaurantId, operationId);
    } catch (error) {
      Logger.error('Failed to update stock levels without transaction', {
        error: error instanceof Error ? error.message : 'Unknown error',
        operationId,
        restaurantId: restaurantId.toString(),
        updates
      });
      throw error;
    }
  }

  /**
   * Perform the actual stock updates
   */
  private static async performStockUpdates(
    updates: StockUpdate[],
    restaurantId: mongoose.Types.ObjectId,
    operationId: string,
    session?: mongoose.ClientSession
  ): Promise<void> {
    for (const update of updates) {
      // Get current item
      const query = Item.findOne({
        _id: update.itemId,
        restaurant: restaurantId
      });

      const item = session ? await query.session(session) : await query;

      if (!item) {
        throw new Error(`Item ${update.itemId} not found`);
      }

      // Calculate new stock levels
      const newRemainingStock = item.remainingStock + update.quantityChange;
      const newUsedStock = item.usedStock - update.quantityChange;

      // Validate stock levels
      if (newRemainingStock < 0) {
        throw new Error(
          `Insufficient stock for ${item.name}. ` +
          `Available: ${item.remainingStock}, Requested: ${Math.abs(update.quantityChange)}`
        );
      }

      if (newUsedStock < 0) {
        throw new Error(`Used stock cannot be negative for ${item.name}`);
      }

      // Update item stock levels
      const updateQuery = Item.updateOne(
        { _id: update.itemId },
        {
          $set: {
            remainingStock: newRemainingStock,
            usedStock: newUsedStock,
            updatedAt: new Date()
          }
        }
      );

      if (session) {
        await updateQuery.session(session);
      } else {
        await updateQuery;
      }

      // Create history record
      const historyData = {
        item: update.itemId,
        quantity: Math.abs(update.quantityChange),
        operationType: update.operationType,
        description: update.description || `${update.operationType} - ${operationId}`,
        date: new Date(),
        ...(update.orderId && { menuItems: update.orderId }),
        ...(update.batchId && { batchId: update.batchId })
      };

      if (session) {
        await ItemHistory.create([historyData], { session });
      } else {
        await ItemHistory.create(historyData);
      }
    }

    Logger.info('Stock levels updated successfully', {
      operationId,
      restaurantId: restaurantId.toString(),
      updatesCount: updates.length
    });
  }

  /**
   * Check if MongoDB supports transactions
   */
  private static async supportsTransactions(): Promise<boolean> {
    try {
      const admin = mongoose.connection.db.admin();
      const result = await admin.serverStatus();

      // Check if we're running on a replica set or sharded cluster
      return !!(result.repl || result.sharding);
    } catch (error) {
      Logger.warn('Could not determine transaction support', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Validate stock availability before processing
   */
  static async validateStockAvailability(
    updates: StockUpdate[],
    restaurantId: mongoose.Types.ObjectId
  ): Promise<{ isValid: boolean; errors: string[] }> {
    const errors: string[] = [];

    for (const update of updates) {
      if (update.quantityChange >= 0) continue; // Only check for consumption

      const item = await Item.findOne({
        _id: update.itemId,
        restaurant: restaurantId
      });

      if (!item) {
        errors.push(`Item ${update.itemId} not found`);
        continue;
      }

      const requiredQuantity = Math.abs(update.quantityChange);
      if (item.remainingStock < requiredQuantity) {
        errors.push(
          `Insufficient stock for ${item.name}. ` +
          `Available: ${item.remainingStock}, Required: ${requiredQuantity}`
        );
      }
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Get current stock levels for multiple items
   */
  static async getCurrentStockLevels(
    itemIds: mongoose.Types.ObjectId[],
    restaurantId: mongoose.Types.ObjectId
  ): Promise<Map<string, { remainingStock: number; usedStock: number; initialStock: number }>> {
    const items = await Item.find({
      _id: { $in: itemIds },
      restaurant: restaurantId
    }).select('_id remainingStock usedStock initialStock');

    const stockMap = new Map();
    items.forEach(item => {
      stockMap.set(item._id.toString(), {
        remainingStock: item.remainingStock,
        usedStock: item.usedStock,
        initialStock: item.initialStock
      });
    });

    return stockMap;
  }

  /**
   * Bulk stock adjustment for inventory management
   */
  static async bulkStockAdjustment(
    adjustments: Array<{
      itemId: mongoose.Types.ObjectId;
      newStock: number;
      reason: string;
    }>,
    restaurantId: mongoose.Types.ObjectId,
    operationId: string
  ): Promise<void> {
    const updates: StockUpdate[] = [];

    // Get current stock levels
    const itemIds = adjustments.map(adj => adj.itemId);
    const currentStocks = await this.getCurrentStockLevels(itemIds, restaurantId);

    // Calculate required updates
    for (const adjustment of adjustments) {
      const currentStock = currentStocks.get(adjustment.itemId.toString());
      if (!currentStock) {
        throw new Error(`Item ${adjustment.itemId} not found`);
      }

      const quantityChange = adjustment.newStock - currentStock.remainingStock;
      if (quantityChange !== 0) {
        updates.push({
          itemId: adjustment.itemId,
          quantityChange,
          operationType: quantityChange > 0 ? 'stock_addition' : 'stock_adjustment',
          description: `${adjustment.reason} - Adjusted to ${adjustment.newStock}`
        });
      }
    }

    if (updates.length > 0) {
      await this.updateStockLevels(updates, restaurantId, operationId);
    }
  }
}
